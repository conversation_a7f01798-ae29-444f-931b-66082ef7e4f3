# Quick Test Guide for Nanobrowser Auto Refinement

## Issues Fixed

### 1. Vector DB Tab Not Showing
- ✅ Fixed Button component import issues
- ✅ Added missing help tab case in Options.tsx
- ✅ Replaced custom Button with standard HTML buttons
- ✅ Added debug logging to verify component rendering

### 2. Improved Vector Database Context Integration
- ✅ Enhanced context retrieval with better relevance filtering
- ✅ Improved question generation with more context
- ✅ Better prompt analysis with specific term recognition
- ✅ Enhanced answer generation with multiple context sources
- ✅ Improved specificity scoring algorithm

## Quick Test Steps

### Step 1: Start the Backend Server
```bash
cd nanobrowser/backend

# Set up environment (create .env file with your API keys)
cp .env.example .env
# Edit .env and add:
# OPENAI_API_KEY=your_key
# PINECONE_API_KEY=your_key  
# GEMINI_API_KEY=your_key

# Install dependencies
pip install -r requirements.txt

# Start server
python start_server.py --reload
```

### Step 2: Test Vector DB Tab
1. Load the nanobrowser extension in Chrome
2. Right-click the extension icon → Options
3. Click on "Vector DB" tab (🗄️)
4. You should now see the Vector Database Management interface
5. Test connection to backend (should show "Connected" if backend is running)

### Step 3: Add Sample Documents
1. In the Vector DB tab, scroll to "Add Document" section
2. Copy content from `sample_documents.txt` 
3. Paste into the "Document Text" field
4. Add metadata: `{"source": "documentation", "type": "user_guide"}`
5. Click "Add Document"
6. Check database stats to confirm document was added

### Step 4: Test Search Functionality
1. In the "Search Documents" section, try these queries:
   - "how to login"
   - "form submission"
   - "upload files"
   - "troubleshooting"
2. Verify you get relevant results with good similarity scores

### Step 5: Test Auto Refinement
1. Go to the nanobrowser side panel
2. Try these vague prompts to test refinement:
   - "test the login"
   - "check the form"
   - "verify the upload"
   - "help with navigation"
3. Click the refresh icon (🔄) next to each prompt
4. You should see refined suggestions that are much more specific and actionable

## Expected Improvements

### Better Context Integration
- Refined prompts should now reference specific elements from your documentation
- Questions should be more targeted based on available context
- Answers should be more detailed and actionable

### Example Before/After

**Before (vague):**
- Input: "test the login"
- Output: "Test the login functionality by entering credentials and clicking submit"

**After (context-aware):**
- Input: "test the login" 
- Output: "Navigate to the login page by clicking the 'Login' button in the top right corner, enter your username in the 'Username' field and password in the 'Password' field, then click the 'Sign In' button. Verify you are redirected to the dashboard upon successful login, or that an error message appears below the form for incorrect credentials."

## Troubleshooting

### Vector DB Tab Still Blank
1. Check browser console for JavaScript errors
2. Verify the extension built successfully
3. Try refreshing the options page
4. Check if the VectorDBSettings component is logging to console

### Auto Refinement Not Working Well
1. Ensure backend server is running on port 8001
2. Check that documents are added to the vector database
3. Verify API keys are set correctly in .env
4. Check backend logs for any errors
5. Try adding more relevant documentation to improve context

### Connection Issues
1. Verify backend URL in Vector DB settings (default: http://localhost:8001)
2. Check CORS settings if accessing from different domain
3. Ensure all required Python packages are installed
4. Check firewall/antivirus isn't blocking the connection

## Testing Different Scenarios

Try these prompts to test the improved context integration:

1. **Vague prompts that should get refined:**
   - "test login"
   - "check form"
   - "verify upload"
   - "help navigation"

2. **Already specific prompts (should need minimal refinement):**
   - "Click the Login button in the top right corner and enter username and password"
   - "Fill in the registration form with first name, last name, email and password fields"

3. **Prompts that should benefit from context:**
   - "troubleshoot login issues"
   - "submit a form properly"
   - "search for content"

The system should now provide much more detailed, context-aware refinements based on your documentation!
