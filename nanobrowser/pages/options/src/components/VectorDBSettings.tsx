import { useState, useEffect, useCallback } from 'react';
// import { Button } from '@extension/ui';

interface VectorDBSettingsProps {
  isDarkMode?: boolean;
}

interface DatabaseStats {
  total_vectors: number;
  dimension: number;
  index_fullness: number;
}

interface SearchResult {
  id: string;
  text: string;
  score: number;
  metadata: Record<string, any>;
}

export const VectorDBSettings = ({ isDarkMode = false }: VectorDBSettingsProps) => {
  const [backendUrl, setBackendUrl] = useState('http://localhost:8001');
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<DatabaseStats | null>(null);

  // Document management
  const [newDocumentText, setNewDocumentText] = useState('');
  const [documentMetadata, setDocumentMetadata] = useState('{}');
  const [isAddingDocument, setIsAddingDocument] = useState(false);

  // Search functionality
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // File upload
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  // Debug: Add console log to verify component is rendering
  console.log('VectorDBSettings component rendering', { isDarkMode, backendUrl });

  // Check backend connection
  const checkConnection = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`${backendUrl}/api/v1/health`);
      if (response.ok) {
        setIsConnected(true);
        // Get database stats
        const statsResponse = await fetch(`${backendUrl}/api/v1/stats`);
        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          setStats(statsData);
        }
      } else {
        setIsConnected(false);
        setError('Backend server not responding');
      }
    } catch (err) {
      setIsConnected(false);
      setError(err instanceof Error ? err.message : 'Connection failed');
    } finally {
      setIsLoading(false);
    }
  }, [backendUrl]);

  // Add document
  const addDocument = useCallback(async () => {
    if (!newDocumentText.trim()) return;
    
    setIsAddingDocument(true);
    setError(null);
    
    try {
      let metadata = {};
      try {
        metadata = JSON.parse(documentMetadata);
      } catch {
        metadata = {};
      }
      
      const response = await fetch(`${backendUrl}/api/v1/documents`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: newDocumentText,
          metadata: metadata,
        }),
      });
      
      if (response.ok) {
        setNewDocumentText('');
        setDocumentMetadata('{}');
        await checkConnection(); // Refresh stats
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Failed to add document');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add document');
    } finally {
      setIsAddingDocument(false);
    }
  }, [newDocumentText, documentMetadata, backendUrl, checkConnection]);

  // Search documents
  const searchDocuments = useCallback(async () => {
    if (!searchQuery.trim()) return;
    
    setIsSearching(true);
    setError(null);
    
    try {
      const response = await fetch(`${backendUrl}/api/v1/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: searchQuery,
          top_k: 5,
        }),
      });
      
      if (response.ok) {
        const data = await response.json();
        setSearchResults(data.results || []);
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Search failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search failed');
    } finally {
      setIsSearching(false);
    }
  }, [searchQuery, backendUrl]);

  // Upload file
  const uploadFile = useCallback(async () => {
    if (!selectedFile) return;
    
    setIsUploading(true);
    setError(null);
    
    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('metadata', JSON.stringify({ filename: selectedFile.name }));
      
      const response = await fetch(`${backendUrl}/api/v1/documents/upload`, {
        method: 'POST',
        body: formData,
      });
      
      if (response.ok) {
        setSelectedFile(null);
        await checkConnection(); // Refresh stats
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'File upload failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'File upload failed');
    } finally {
      setIsUploading(false);
    }
  }, [selectedFile, backendUrl, checkConnection]);

  // Clear database
  const clearDatabase = useCallback(async () => {
    if (!confirm('Are you sure you want to clear all documents from the database? This action cannot be undone.')) {
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`${backendUrl}/api/v1/clear`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        await checkConnection(); // Refresh stats
        setSearchResults([]);
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Failed to clear database');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to clear database');
    } finally {
      setIsLoading(false);
    }
  }, [backendUrl, checkConnection]);

  // Check connection on mount and when URL changes
  useEffect(() => {
    checkConnection();
  }, [checkConnection]);

  return (
    <div className="space-y-6 p-6">
      <div>
        <h2 className={`mb-4 text-2xl font-bold ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
          Vector Database Management
        </h2>
        <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          Manage your Pinecone vector database for prompt refinement
        </p>
      </div>

      {/* Backend Connection */}
      <div className={`rounded-lg border p-4 ${isDarkMode ? 'border-slate-700 bg-slate-800' : 'border-gray-200 bg-white'}`}>
        <h3 className={`mb-3 text-lg font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
          Backend Connection
        </h3>
        
        <div className="space-y-3">
          <div>
            <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Backend URL
            </label>
            <input
              type="url"
              value={backendUrl}
              onChange={(e) => setBackendUrl(e.target.value)}
              className={`mt-1 block w-full rounded-md border px-3 py-2 ${
                isDarkMode
                  ? 'border-slate-600 bg-slate-700 text-gray-200'
                  : 'border-gray-300 bg-white text-gray-900'
              }`}
              placeholder="http://localhost:8001"
            />
          </div>
          
          <div className="flex items-center gap-3">
            <button
              onClick={checkConnection}
              disabled={isLoading}
              className={`px-4 py-2 rounded-md font-medium transition-colors ${
                isDarkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'
              } text-white disabled:opacity-50 disabled:cursor-not-allowed`}>
              {isLoading ? 'Checking...' : 'Test Connection'}
            </button>
            
            <div className="flex items-center gap-2">
              <div
                className={`h-3 w-3 rounded-full ${
                  isConnected ? 'bg-green-500' : 'bg-red-500'
                }`}
              />
              <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className={`rounded-lg border p-4 ${isDarkMode ? 'border-red-600 bg-red-900/20' : 'border-red-200 bg-red-50'}`}>
          <p className={`text-sm ${isDarkMode ? 'text-red-400' : 'text-red-700'}`}>
            {error}
          </p>
        </div>
      )}

      {/* Database Stats */}
      {isConnected && stats && (
        <div className={`rounded-lg border p-4 ${isDarkMode ? 'border-slate-700 bg-slate-800' : 'border-gray-200 bg-white'}`}>
          <h3 className={`mb-3 text-lg font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
            Database Statistics
          </h3>

          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className={`text-2xl font-bold ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`}>
                {stats.total_vectors.toLocaleString()}
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Total Vectors
              </div>
            </div>

            <div className="text-center">
              <div className={`text-2xl font-bold ${isDarkMode ? 'text-green-400' : 'text-green-600'}`}>
                {stats.dimension}
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Dimensions
              </div>
            </div>

            <div className="text-center">
              <div className={`text-2xl font-bold ${isDarkMode ? 'text-purple-400' : 'text-purple-600'}`}>
                {(stats.index_fullness * 100).toFixed(1)}%
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Index Fullness
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Document */}
      {isConnected && (
        <div className={`rounded-lg border p-4 ${isDarkMode ? 'border-slate-700 bg-slate-800' : 'border-gray-200 bg-white'}`}>
          <h3 className={`mb-3 text-lg font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
            Add Document
          </h3>

          <div className="space-y-3">
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Document Text
              </label>
              <textarea
                value={newDocumentText}
                onChange={(e) => setNewDocumentText(e.target.value)}
                rows={4}
                className={`mt-1 block w-full rounded-md border px-3 py-2 ${
                  isDarkMode
                    ? 'border-slate-600 bg-slate-700 text-gray-200'
                    : 'border-gray-300 bg-white text-gray-900'
                }`}
                placeholder="Enter document text..."
              />
            </div>

            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Metadata (JSON)
              </label>
              <input
                type="text"
                value={documentMetadata}
                onChange={(e) => setDocumentMetadata(e.target.value)}
                className={`mt-1 block w-full rounded-md border px-3 py-2 ${
                  isDarkMode
                    ? 'border-slate-600 bg-slate-700 text-gray-200'
                    : 'border-gray-300 bg-white text-gray-900'
                }`}
                placeholder='{"category": "example", "source": "manual"}'
              />
            </div>

            <button
              onClick={addDocument}
              disabled={isAddingDocument || !newDocumentText.trim()}
              className={`px-4 py-2 rounded-md font-medium transition-colors ${
                isDarkMode ? 'bg-green-600 hover:bg-green-700' : 'bg-green-500 hover:bg-green-600'
              } text-white disabled:opacity-50 disabled:cursor-not-allowed`}>
              {isAddingDocument ? 'Adding...' : 'Add Document'}
            </button>
          </div>
        </div>
      )}

      {/* File Upload */}
      {isConnected && (
        <div className={`rounded-lg border p-4 ${isDarkMode ? 'border-slate-700 bg-slate-800' : 'border-gray-200 bg-white'}`}>
          <h3 className={`mb-3 text-lg font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
            Upload File
          </h3>

          <div className="space-y-3">
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Select File (.txt, .md)
              </label>
              <input
                type="file"
                accept=".txt,.md"
                onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
                className={`mt-1 block w-full rounded-md border px-3 py-2 ${
                  isDarkMode
                    ? 'border-slate-600 bg-slate-700 text-gray-200'
                    : 'border-gray-300 bg-white text-gray-900'
                }`}
              />
            </div>

            <button
              onClick={uploadFile}
              disabled={isUploading || !selectedFile}
              className={`px-4 py-2 rounded-md font-medium transition-colors ${
                isDarkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'
              } text-white disabled:opacity-50 disabled:cursor-not-allowed`}>
              {isUploading ? 'Uploading...' : 'Upload File'}
            </button>
          </div>
        </div>
      )}

      {/* Search Documents */}
      {isConnected && (
        <div className={`rounded-lg border p-4 ${isDarkMode ? 'border-slate-700 bg-slate-800' : 'border-gray-200 bg-white'}`}>
          <h3 className={`mb-3 text-lg font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
            Search Documents
          </h3>

          <div className="space-y-3">
            <div className="flex gap-2">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={`flex-1 rounded-md border px-3 py-2 ${
                  isDarkMode
                    ? 'border-slate-600 bg-slate-700 text-gray-200'
                    : 'border-gray-300 bg-white text-gray-900'
                }`}
                placeholder="Enter search query..."
                onKeyDown={(e) => e.key === 'Enter' && searchDocuments()}
              />
              <button
                onClick={searchDocuments}
                disabled={isSearching || !searchQuery.trim()}
                className={`px-4 py-2 rounded-md font-medium transition-colors ${
                  isDarkMode ? 'bg-purple-600 hover:bg-purple-700' : 'bg-purple-500 hover:bg-purple-600'
                } text-white disabled:opacity-50 disabled:cursor-not-allowed`}>
                {isSearching ? 'Searching...' : 'Search'}
              </button>
            </div>

            {/* Search Results */}
            {searchResults.length > 0 && (
              <div className="space-y-2">
                <h4 className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Search Results ({searchResults.length})
                </h4>
                {searchResults.map((result) => (
                  <div
                    key={result.id}
                    className={`rounded border p-3 ${
                      isDarkMode ? 'border-slate-600 bg-slate-700' : 'border-gray-200 bg-gray-50'
                    }`}>
                    <div className="flex items-center justify-between mb-2">
                      <span className={`text-xs font-mono ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        ID: {result.id.substring(0, 8)}...
                      </span>
                      <span className={`text-xs ${isDarkMode ? 'text-green-400' : 'text-green-600'}`}>
                        Score: {result.score.toFixed(3)}
                      </span>
                    </div>
                    <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      {result.text.substring(0, 200)}
                      {result.text.length > 200 && '...'}
                    </p>
                    {Object.keys(result.metadata).length > 0 && (
                      <div className="mt-2">
                        <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          Metadata: {JSON.stringify(result.metadata)}
                        </span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Danger Zone */}
      {isConnected && (
        <div className={`rounded-lg border p-4 ${isDarkMode ? 'border-red-600 bg-red-900/20' : 'border-red-200 bg-red-50'}`}>
          <h3 className={`mb-3 text-lg font-semibold ${isDarkMode ? 'text-red-400' : 'text-red-700'}`}>
            Danger Zone
          </h3>

          <div className="space-y-3">
            <p className={`text-sm ${isDarkMode ? 'text-red-300' : 'text-red-600'}`}>
              Clear all documents from the vector database. This action cannot be undone.
            </p>

            <button
              onClick={clearDatabase}
              disabled={isLoading}
              className="px-4 py-2 rounded-md font-medium transition-colors bg-red-600 hover:bg-red-700 text-white disabled:opacity-50 disabled:cursor-not-allowed">
              {isLoading ? 'Clearing...' : 'Clear Database'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
