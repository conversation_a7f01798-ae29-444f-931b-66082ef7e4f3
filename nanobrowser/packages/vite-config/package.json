{"name": "@extension/vite-config", "version": "0.1.8", "description": "chrome extension - vite base configuration", "main": "index.mjs", "type": "module", "private": true, "scripts": {"clean:node_modules": "pnpx rimraf node_modules", "clean": "pnpm clean:node_modules"}, "devDependencies": {"@extension/hmr": "workspace:*", "@extension/tsconfig": "workspace:*", "@vitejs/plugin-react-swc": "^3.7.2", "deepmerge": "^4.3.1"}}