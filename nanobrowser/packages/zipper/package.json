{"name": "@extension/zipper", "version": "0.1.8", "description": "chrome extension - zipper", "private": true, "sideEffects": false, "files": ["dist/**"], "main": "dist/index.js", "module": "dist/index.js", "types": "index.ts", "scripts": {"clean:bundle": "<PERSON><PERSON><PERSON> dist", "clean:node_modules": "pnpx rimraf node_modules", "clean:turbo": "rimraf .turbo", "clean": "pnpm clean:bundle && pnpm clean:node_modules && pnpm clean:turbo", "zip": "tsx index.ts", "ready": "tsc", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "pnpm lint --fix", "prettier": "prettier . --write --ignore-path ../../.prettierignore", "type-check": "tsc --noEmit"}, "devDependencies": {"@extension/tsconfig": "workspace:*", "fast-glob": "^3.3.2", "fflate": "^0.8.2", "tsx": "^4.19.2"}}