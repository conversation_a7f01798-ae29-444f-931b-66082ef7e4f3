{"name": "@extension/storage", "version": "0.1.8", "description": "chrome extension - storage", "private": true, "sideEffects": false, "files": ["dist/**"], "main": "./dist/index.js", "types": "index.ts", "scripts": {"clean:bundle": "<PERSON><PERSON><PERSON> dist", "clean:node_modules": "pnpx rimraf node_modules", "clean:turbo": "rimraf .turbo", "clean": "pnpm clean:bundle && pnpm clean:node_modules && pnpm clean:turbo", "ready": "node build.mjs", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "pnpm lint --fix", "prettier": "prettier . --write --ignore-path ../../.prettierignore", "type-check": "tsc --noEmit"}, "devDependencies": {"@extension/tsconfig": "workspace:*"}}